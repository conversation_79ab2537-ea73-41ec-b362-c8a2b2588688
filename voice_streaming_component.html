<!DOCTYPE html>
<html>
<head>
    <title>Voice Streaming Component</title>
    <style>
        .voice-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: #f0f2f6;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ccc;
            transition: all 0.3s ease;
        }
        
        .status-indicator.recording {
            background: #ff4444;
            animation: pulse 1s infinite;
        }
        
        .status-indicator.processing {
            background: #ffaa00;
        }
        
        .status-indicator.ready {
            background: #44ff44;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .voice-button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .voice-button.start {
            background: #4CAF50;
            color: white;
        }
        
        .voice-button.stop {
            background: #f44336;
            color: white;
        }
        
        .voice-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status-text {
            font-weight: bold;
            margin-left: 10px;
        }
        
        .audio-level {
            width: 200px;
            height: 10px;
            background: #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .audio-level-bar {
            height: 100%;
            background: linear-gradient(to right, #4CAF50, #ffaa00, #ff4444);
            width: 0%;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="voice-controls">
        <div class="status-indicator" id="statusIndicator"></div>
        <button class="voice-button start" id="startBtn" onclick="startStreaming()">🎙️ Start Streaming</button>
        <button class="voice-button stop" id="stopBtn" onclick="stopStreaming()" disabled>⏹️ Stop Streaming</button>
        <div class="status-text" id="statusText">Ready to start</div>
        <div class="audio-level">
            <div class="audio-level-bar" id="audioLevelBar"></div>
        </div>
    </div>
    
    <div id="transcriptionOutput" style="margin-top: 20px; padding: 15px; background: #fff; border-radius: 10px; min-height: 200px; max-height: 400px; overflow-y: auto; border: 2px solid #ddd;">
        <div style="text-align: center; color: #666; font-style: italic;">
            🎙️ Click "Start Streaming" and say anything to begin your cash receipt entry...
        </div>
    </div>

    <script>
        let mediaRecorder;
        let audioContext;
        let analyser;
        let microphone;
        let websocket;
        let isStreaming = false;
        let audioChunks = [];
        let scriptProcessor;
        let isAISpeaking = false;
        
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const audioLevelBar = document.getElementById('audioLevelBar');
        const transcriptionOutput = document.getElementById('transcriptionOutput');
        
        // WebSocket connection
        function connectWebSocket() {
            console.log('Attempting to connect to WebSocket...');
            try {
                websocket = new WebSocket('ws://localhost:8765');

                websocket.onopen = function(event) {
                    console.log('WebSocket connected successfully');
                    updateStatus('ready', 'Connected to voice server');
                    startBtn.disabled = false; // Enable start button when connected
                };

                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                websocket.onclose = function(event) {
                    console.log('WebSocket disconnected:', event.code, event.reason);
                    updateStatus('disconnected', 'Disconnected from voice server');
                    startBtn.disabled = true; // Disable start button when disconnected

                    // Try to reconnect after 3 seconds
                    setTimeout(connectWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateStatus('error', 'Connection error - retrying...');
                    startBtn.disabled = true;
                };
            } catch (error) {
                console.error('Failed to create WebSocket:', error);
                updateStatus('error', 'Failed to connect to voice server');
                startBtn.disabled = true;

                // Try to reconnect after 5 seconds
                setTimeout(connectWebSocket, 5000);
            }
        }
        
        function handleWebSocketMessage(data) {
            console.log('Received:', data);

            if (data.status === 'transcribed' && data.text) {
                displayTranscription(data.text, 'user');

                // Display and play AI response if available
                if (data.ai_response) {
                    setTimeout(() => {
                        displayTranscription(data.ai_response, 'assistant');

                        // Automatically play AI audio response
                        if (data.ai_audio) {
                            playAIResponse(data.ai_audio);
                        }
                    }, 500); // Small delay for better UX
                }
            } else if (data.status === 'recording') {
                updateAudioLevel(data.rms || 0);
            }
        }
        
        function displayTranscription(text, sender = 'user') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = sender === 'user' ? '🗣️' : '🤖';
            const label = sender === 'user' ? 'You' : 'Assistant';
            const style = sender === 'user' ? 'color: #0066cc; font-weight: bold;' : 'color: #009900; font-weight: bold;';
            const audioIndicator = sender === 'assistant' ? ' 🔊' : '';

            transcriptionOutput.innerHTML += `
                <div style="margin: 10px 0; padding: 10px; border-left: 3px solid ${sender === 'user' ? '#0066cc' : '#009900'}; background: ${sender === 'user' ? '#f0f8ff' : '#f0fff0'};">
                    <strong style="${style}">${icon} ${label}${audioIndicator} [${timestamp}]:</strong><br>
                    <span style="margin-left: 20px;">${text}</span>
                </div>
            `;
            transcriptionOutput.scrollTop = transcriptionOutput.scrollHeight;
        }
        
        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }
        
        function updateAudioLevel(rms) {
            const level = Math.min(rms * 1000, 100); // Scale RMS to percentage
            audioLevelBar.style.width = `${level}%`;
        }

        function playAIResponse(audioBase64) {
            try {
                // Set AI speaking state to prevent microphone feedback
                isAISpeaking = true;

                // Create audio element and play immediately
                const audio = new Audio();
                audio.src = `data:audio/mp3;base64,${audioBase64}`;
                audio.autoplay = true;
                audio.volume = 0.8; // Slightly lower volume for comfort

                // Add visual indicator while playing
                updateStatus('processing', '🔊 AI is speaking...');

                audio.onended = function() {
                    isAISpeaking = false;
                    updateStatus('recording', '🎙️ Listening for your response...');
                };

                audio.onerror = function(e) {
                    console.error('Error playing AI audio:', e);
                    isAISpeaking = false;
                    updateStatus('recording', 'Audio playback failed - ready for input');
                };

                // Play the audio
                audio.play().catch(e => {
                    console.error('Failed to play audio:', e);
                    isAISpeaking = false;
                    updateStatus('recording', 'Ready for input');
                });

            } catch (error) {
                console.error('Error creating audio element:', error);
                isAISpeaking = false;
                updateStatus('recording', 'Ready for input');
            }
        }
        
        async function startStreaming() {
            console.log('Start Streaming button clicked');

            // Check WebSocket connection first
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                console.error('WebSocket not connected');
                updateStatus('error', 'Not connected to voice server');
                alert('Please wait for connection to voice server');
                return;
            }

            try {
                console.log('Requesting microphone access...');
                updateStatus('processing', 'Requesting microphone access...');

                // Check if getUserMedia is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not supported in this browser/context');
                }

                // Check current permission state first
                try {
                    const permissionStatus = await navigator.permissions.query({ name: 'microphone' });
                    console.log('Current microphone permission state:', permissionStatus.state);

                    if (permissionStatus.state === 'denied') {
                        throw new Error('Microphone permission is denied. Please enable microphone access in your browser settings.');
                    }
                } catch (permError) {
                    console.log('Permission query not supported or failed:', permError.message);
                    // Continue anyway - permission query might not be supported
                }

                // Add a timeout to prevent hanging (increased to 30 seconds for widget environment)
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Microphone access request timed out')), 30000);
                });

                // Request microphone access with timeout and more permissive settings
                const stream = await Promise.race([
                    navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    }),
                    timeoutPromise
                ]);

                console.log('Microphone access granted');

                // Set up audio context for real-time processing
                audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });

                analyser = audioContext.createAnalyser();
                microphone = audioContext.createMediaStreamSource(stream);

                // Create a script processor for raw audio data
                scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                microphone.connect(analyser);
                microphone.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);

                // Process audio in real-time
                scriptProcessor.onaudioprocess = function(event) {
                    // Skip processing if AI is speaking to avoid feedback
                    if (isAISpeaking) {
                        return;
                    }

                    const inputBuffer = event.inputBuffer;
                    const inputData = inputBuffer.getChannelData(0);

                    // Convert to base64 and send
                    const float32Array = new Float32Array(inputData);
                    const arrayBuffer = float32Array.buffer;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

                    // Send to WebSocket server
                    if (websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(JSON.stringify({
                            type: 'audio_chunk',
                            audio_data: base64Audio
                        }));
                    }
                };

                isStreaming = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                updateStatus('recording', 'Streaming active - speak now');

                // Send start signal to WebSocket
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({ type: 'start_recording' }));
                }

                // Start audio level monitoring
                monitorAudioLevel();

            } catch (error) {
                console.error('Error starting stream:', error);

                let errorMessage = 'Failed to start streaming';
                let userMessage = 'Error accessing microphone: ' + error.message;

                // Handle specific error types
                if (error.name === 'NotAllowedError') {
                    errorMessage = 'Microphone access denied';
                    userMessage = 'Please allow microphone access and try again.\n\nIn the widget, microphone permission should be granted automatically.\nIf this keeps happening, try restarting the widget.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = 'No microphone found';
                    userMessage = 'No microphone device found on this system.\nPlease connect a microphone and try again.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage = 'Microphone not supported';
                    userMessage = 'Microphone access is not supported in this context.\nThis might be a limitation of the embedded browser.';
                } else if (error.message.includes('timeout')) {
                    errorMessage = 'Permission request timed out';
                    userMessage = 'Microphone permission request timed out.\nPlease try again or restart the widget.';
                }

                updateStatus('error', errorMessage);

                // Don't show alert immediately, give user a chance to see the status
                setTimeout(() => {
                    alert(userMessage);
                }, 500);

                // Re-enable the start button
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        function stopStreaming() {
            if (audioContext) {
                audioContext.close();
            }

            isStreaming = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            updateStatus('ready', 'Streaming stopped');

            // Send stop signal to WebSocket
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({ type: 'stop_recording' }));
            }
        }
        

        
        function monitorAudioLevel() {
            if (!isStreaming || !analyser) return;
            
            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            
            function updateLevel() {
                if (!isStreaming) return;
                
                analyser.getByteFrequencyData(dataArray);
                const average = dataArray.reduce((a, b) => a + b) / bufferLength;
                updateAudioLevel(average / 255);
                
                requestAnimationFrame(updateLevel);
            }
            
            updateLevel();
        }
        
        // Global error handler to prevent crashes
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('Global JavaScript error:', message, 'at', source, 'line', lineno);
            updateStatus('error', 'JavaScript error occurred');

            // Don't let the error crash the page
            return true;
        };

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            updateStatus('error', 'Promise rejection occurred');

            // Prevent the error from being logged to console as unhandled
            event.preventDefault();
        });

        // Initialize WebSocket connection when page loads
        window.onload = function() {
            console.log('Page loaded, initializing...');

            // Disable start button initially
            startBtn.disabled = true;
            updateStatus('processing', 'Connecting to voice server...');

            // Connect to WebSocket
            connectWebSocket();
        };
        
        // Handle page unload
        window.onbeforeunload = function() {
            if (isStreaming) {
                stopStreaming();
            }
            if (websocket) {
                websocket.close();
            }
        };
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
Test script to verify widget fixes
"""

import time
import asyncio
import websockets
import json
import psutil
import socket

def check_widget_running():
    """Check if widget is running"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == 'VoiceStreamingWidget.exe':
            processes.append(proc.info)
    return len(processes) > 0, processes

def check_port_available():
    """Check if port 8765 is available"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8765))
        sock.close()
        return result == 0
    except:
        return False

async def test_websocket_timing():
    """Test WebSocket connection timing"""
    print("Testing WebSocket connection timing...")
    
    for attempt in range(5):
        try:
            print(f"Attempt {attempt + 1}: Connecting to ws://localhost:8765")
            start_time = time.time()
            
            async with websockets.connect("ws://localhost:8765", timeout=3) as websocket:
                connect_time = time.time() - start_time
                print(f"✓ Connected successfully in {connect_time:.2f} seconds")
                
                # Send test message
                await websocket.send(json.dumps({"type": "ping"}))
                response = await asyncio.wait_for(websocket.recv(), timeout=2)
                print(f"✓ Received response: {response}")
                return True
                
        except Exception as e:
            print(f"✗ Attempt {attempt + 1} failed: {e}")
            if attempt < 4:
                print("Waiting 2 seconds before retry...")
                await asyncio.sleep(2)
    
    return False

def main():
    """Main test function"""
    print("Widget Fixes Test Script")
    print("=" * 40)
    
    # Check if widget is running
    widget_running, processes = check_widget_running()
    if not widget_running:
        print("❌ Widget not running. Please start the widget first.")
        print("Run: .\\dist\\VoiceStreamingWidget.exe")
        return
    
    print(f"✅ Widget is running ({len(processes)} process(es))")
    
    print("\n📋 Test Instructions:")
    print("1. Make sure the widget window is visible")
    print("2. Click 'Start Server' button")
    print("3. Wait for server to start")
    print("4. Press Enter to test WebSocket timing")
    
    input("\nPress Enter when server is started...")
    
    # Test port availability
    if not check_port_available():
        print("❌ Port 8765 is not available. Server may not be running.")
        return
    
    print("✅ Port 8765 is available")
    
    # Test WebSocket timing
    success = asyncio.run(test_websocket_timing())
    
    if success:
        print("\n✅ WebSocket timing test passed!")
        print("The server startup race condition appears to be fixed.")
    else:
        print("\n❌ WebSocket timing test failed!")
        print("There may still be connection issues.")
    
    print("\n📋 Next Steps:")
    print("1. Try clicking 'Start Streaming' in the widget")
    print("2. Check if microphone access works without timeout")
    print("3. Test the complete voice workflow")

if __name__ == "__main__":
    main()
